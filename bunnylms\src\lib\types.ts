// Bunny.net API types
export interface BunnyVideo {
  videoLibraryId: number;
  guid: string;
  title: string;
  dateUploaded: string;
  views: number;
  isPublic: boolean;
  length: number;
  status: number;
  framerate: number;
  width: number;
  height: number;
  availableResolutions: string;
  thumbnailCount: number;
  encodeProgress: number;
  storageSize: number;
  captions: any[];
  hasMP4Fallback: boolean;
  collectionId: string | null;
  thumbnailFileName: string;
  averageWatchTime: number;
  totalWatchTime: number;
  category: string | null;
  chapters: any[];
  moments: any[];
  metaTags: any[];
  transcodingMessages: any[];
}

export interface CreateVideoResponse {
  success: boolean;
  statusCode: number;
  video: BunnyVideo;
}

export interface UploadProgressData {
  bytesUploaded: number;
  bytesTotal: number;
  percentage: number;
}

export interface VideoFormData {
  title: string;
  description?: string;
  category?: string;
  isPublic: boolean;
}
