import { Metadata } from "next";
import VideoUploadForm from "@/components/video-upload-form";

export const metadata: Metadata = {
  title: "Upload Video | BunnyLMS",
  description: "Upload your educational videos to BunnyLMS",
};

export default function UploadPage() {
  return (
    <div className="container mx-auto py-10">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Upload Video</h1>
        <p className="text-muted-foreground mb-8">
          Upload your educational videos to share with your students. We support most video formats.
        </p>
        <VideoUploadForm />
      </div>
    </div>
  );
}
