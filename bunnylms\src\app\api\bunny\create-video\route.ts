import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

// Access environment variables
const BUNNY_API_KEY = process.env.BUNNY_API_KEY;
const BUNNY_LIBRARY_ID = process.env.BUNNY_LIBRARY_ID;

// Debug log - remove in production
console.log("API Route Called");
console.log("API Key exists:", !!BUNNY_API_KEY);
console.log("Library ID:", BUNNY_LIBRARY_ID);

if (!BUNNY_API_KEY || !BUNNY_LIBRARY_ID) {
  console.error("Missing Bunny.net API credentials in environment variables!");
}

export async function POST(request: NextRequest) {
  try {
    const { title, collectionId } = await request.json();

    if (!title) {
      return NextResponse.json(
        { error: "Title is required" },
        { status: 400 }
      );
    }

    // Validate environment variables
    if (!BUNNY_API_KEY || !BUNNY_LIBRARY_ID) {
      console.error("Missing required Bunny.net credentials");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }
    
    // Log request for debugging
    console.log(`Creating video with title: ${title} in library: ${BUNNY_LIBRARY_ID}`);
    
    // Create video in Bunny.net
    const response = await fetch(`https://video.bunnycdn.com/library/${BUNNY_LIBRARY_ID}/videos`, {
      method: "POST",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "AccessKey": BUNNY_API_KEY
      },
      body: JSON.stringify({
        title,
        collectionId: collectionId || null
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Bunny.net API error:", errorText);
      return NextResponse.json(
        { error: `Failed to create video: ${response.statusText}` },
        { status: response.status }
      );
    }

    const videoData = await response.json();
    
    // Generate upload signature
    const expirationTime = Math.floor(Date.now() / 1000) + 3600; // 1 hour from now
    
    // Ensure we have all the required data
    if (!videoData || !videoData.guid) {
      console.error("Missing video data or GUID in response", videoData);
      return NextResponse.json(
        { error: "Invalid response from Bunny.net API" },
        { status: 500 }
      );
    }
    
    // For better debugging
    console.log("Video created successfully with ID:", videoData.guid);
    
    // Create signature
    const dataToSign = `${BUNNY_LIBRARY_ID}${BUNNY_API_KEY}${expirationTime}${videoData.guid}`;
    const signature = crypto
      .createHash('sha256')
      .update(dataToSign)
      .digest('hex');
      
    console.log("Generated signature successfully");

    return NextResponse.json({
      success: true,
      video: videoData,
      uploadSignature: signature,
      expirationTime,
      libraryId: BUNNY_LIBRARY_ID,
      videoId: videoData.guid
    });
  } catch (error) {
    console.error("Error in create-video API route:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
