"use client";

import { useState, useRef, FormEvent } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import * as tus from "tus-js-client";

type UploadProgressData = {
  bytesUploaded: number;
  bytesTotal: number;
  percentage: number;
};

export default function VideoUploadForm() {
  const [file, setFile] = useState<File | null>(null);
  const [title, setTitle] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgressData | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const uploadRef = useRef<tus.Upload | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    setFile(selectedFile);
    setUploadError(null);
    setUploadSuccess(false);
    
    // Auto-fill title with filename (without extension)
    if (selectedFile && !title) {
      const fileName = selectedFile.name.split(".").slice(0, -1).join(".");
      setTitle(fileName);
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    if (!file) {
      setUploadError("Please select a video file to upload");
      return;
    }

    try {
      setIsUploading(true);
      setUploadError(null);
      
      // Step 1: Create a video object in Bunny.net via our API
      console.log("Submitting video creation request with title:", data.title);
      
      const createResponse = await fetch("/api/bunny/create-video", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: data.title,
          // Add other metadata as needed
        }),
      });

      console.log("Create video response status:", createResponse.status);
      
      if (!createResponse.ok) {
        const errorText = await createResponse.text();
        console.error("Error response:", errorText);
        
        let errorMessage = "Failed to create video";
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (_) {
          // If the response isn't valid JSON, use the raw text
          errorMessage += ": " + (errorText || createResponse.statusText);
        }
        
        throw new Error(errorMessage);
      }

      const { 
        videoId, 
        libraryId, 
        uploadSignature, 
        expirationTime 
      } = await createResponse.json();

      // Step 2: Upload the video using TUS protocol
      uploadRef.current = new tus.Upload(file, {
        endpoint: "https://video.bunnycdn.com/tusupload",
        retryDelays: [0, 3000, 5000, 10000, 20000, 60000],
        metadata: {
          filetype: file.type,
          title: data.title,
          // Add collection ID if needed
        },
        headers: {
          AuthorizationSignature: uploadSignature,
          AuthorizationExpire: expirationTime.toString(),
          VideoId: videoId,
          LibraryId: libraryId,
        },
        onError: (error) => {
          console.error("Upload error:", error);
          setUploadError(`Upload failed: ${error.message || "Unknown error"}`);
          setIsUploading(false);
        },
        onProgress: (bytesUploaded, bytesTotal) => {
          const percentage = Math.round((bytesUploaded / bytesTotal) * 100);
          setUploadProgress({
            bytesUploaded,
            bytesTotal,
            percentage,
          });
        },
        onSuccess: () => {
          setUploadSuccess(true);
          setIsUploading(false);
          form.reset();
          setFile(null);
        },
      });

      // Check for previous uploads to resume
      uploadRef.current.findPreviousUploads().then((previousUploads) => {
        if (previousUploads.length) {
          uploadRef.current?.resumeFromPreviousUpload(previousUploads[0]);
        }
        
        // Start the upload
        uploadRef.current?.start();
      });
    } catch (error) {
      console.error("Upload setup error:", error);
      setUploadError(`Failed to initialize upload: ${error instanceof Error ? error.message : "Unknown error"}`);
      setIsUploading(false);
    }
  };

  const cancelUpload = () => {
    if (uploadRef.current) {
      uploadRef.current.abort();
      setIsUploading(false);
      setUploadProgress(null);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className="space-y-6">
      {uploadSuccess ? (
        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <h3 className="text-green-800 dark:text-green-400 font-medium mb-2">Upload Successful!</h3>
          <p className="text-green-700 dark:text-green-300 mb-4">
            Your video has been uploaded successfully and is now being processed.
          </p>
          <Button
            onClick={() => {
              setUploadSuccess(false);
              setUploadProgress(null);
            }}
          >
            Upload Another Video
          </Button>
        </div>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* File Upload */}
            <div className="space-y-2">
              <FormLabel>Video File</FormLabel>
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-900/50 transition-colors">
                <input
                  type="file"
                  id="video"
                  accept="video/*"
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={isUploading}
                />
                <label htmlFor="video" className="cursor-pointer">
                  {file ? (
                    <div className="space-y-2">
                      <p className="text-sm font-medium">{file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatBytes(file.size)} • {file.type}
                      </p>
                      <Button type="button" variant="outline" size="sm">
                        Change File
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="w-6 h-6 text-primary"
                        >
                          <path d="m21 12-7-7v4H3v6h11v4z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-sm font-medium">
                          Drag and drop your video or click to browse
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Supports MP4, MOV, AVI, and other common video formats
                        </p>
                      </div>
                    </div>
                  )}
                </label>
              </div>
            </div>

            {/* Video Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter video title" {...field} disabled={isUploading} />
                  </FormControl>
                  <FormDescription>
                    Give your video a descriptive title
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Video Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <textarea
                      className="flex min-h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Enter video description"
                      {...field}
                      disabled={isUploading}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide details about your video content
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category */}
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Programming, Math, Science" {...field} disabled={isUploading} />
                  </FormControl>
                  <FormDescription>
                    Categorize your video for better organization
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Upload Progress */}
            {uploadProgress && (
              <Card className="mt-4">
                <CardContent className="pt-6">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Uploading...</span>
                      <span>{uploadProgress.percentage}%</span>
                    </div>
                    <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-primary"
                        style={{ width: `${uploadProgress.percentage}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatBytes(uploadProgress.bytesUploaded)} of {formatBytes(uploadProgress.bytesTotal)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Error Message */}
            {uploadError && (
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <p className="text-red-700 dark:text-red-300">{uploadError}</p>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex gap-4">
              <Button type="submit" disabled={isUploading || !file}>
                {isUploading ? "Uploading..." : "Upload Video"}
              </Button>
              {isUploading && (
                <Button type="button" variant="outline" onClick={cancelUpload}>
                  Cancel Upload
                </Button>
              )}
            </div>
          </form>
        </Form>
      )}
    </div>
  );
}
