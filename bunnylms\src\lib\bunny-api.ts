import { CreateVideoResponse, BunnyVide<PERSON> } from "./types";

// These would typically come from environment variables
const BUNNY_API_KEY = process.env.NEXT_PUBLIC_BUNNY_API_KEY || "";
const BUNNY_LIBRARY_ID = process.env.NEXT_PUBLIC_BUNNY_LIBRARY_ID || "";

const BASE_URL = "https://video.bunnycdn.com/library";

/**
 * Creates a new video object in Bunny.net
 * This must be done before uploading the actual video content
 */
export async function createVideo(title: string, collectionId?: string): Promise<CreateVideoResponse> {
  try {
    const response = await fetch(`${BASE_URL}/${BUNNY_LIBRARY_ID}/videos`, {
      method: "POST",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "AccessKey": BUNNY_API_KEY
      },
      body: JSON.stringify({
        title,
        collectionId: collectionId || null
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to create video: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      success: true,
      statusCode: response.status,
      video: data as BunnyVideo
    };
  } catch (error) {
    console.error("Error creating video:", error);
    return {
      success: false,
      statusCode: 500,
      video: {} as BunnyVideo
    };
  }
}

/**
 * Generates a signature for the TUS upload
 * This should be done server-side for security, but for demo purposes we're doing it client-side
 */
export function generateSignature(libraryId: string, apiKey: string, expirationTime: number, videoId: string): string {
  // In a real application, this should be done server-side
  // For demo purposes, we're showing the logic, but you should implement this in a server API route
  
  // This is a placeholder - in production, use a proper SHA256 implementation
  // and move this logic to a server-side API route
  return `DEMO_SIGNATURE_${libraryId}_${videoId}_${expirationTime}`;
}

/**
 * Gets a list of videos from the library
 */
export async function getVideos(page = 1, perPage = 10): Promise<BunnyVideo[]> {
  try {
    const response = await fetch(
      `${BASE_URL}/${BUNNY_LIBRARY_ID}/videos?page=${page}&itemsPerPage=${perPage}`, 
      {
        headers: {
          "Accept": "application/json",
          "AccessKey": BUNNY_API_KEY
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch videos: ${response.statusText}`);
    }

    const data = await response.json();
    return data.items as BunnyVideo[];
  } catch (error) {
    console.error("Error fetching videos:", error);
    return [];
  }
}

/**
 * Deletes a video from the library
 */
export async function deleteVideo(videoId: string): Promise<boolean> {
  try {
    const response = await fetch(
      `${BASE_URL}/${BUNNY_LIBRARY_ID}/videos/${videoId}`, 
      {
        method: "DELETE",
        headers: {
          "Accept": "application/json",
          "AccessKey": BUNNY_API_KEY
        }
      }
    );

    return response.ok;
  } catch (error) {
    console.error("Error deleting video:", error);
    return false;
  }
}
